const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
const SANCTUM_URL = import.meta.env.VITE_SANCTUM_URL || 'http://localhost:8000';
interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
}

class ApiService {
  private baseURL: string;
  private sanctumURL: string;

  constructor(baseURL: string, sanctumURL: string) {
    this.baseURL = baseURL;
    this.sanctumURL = sanctumURL;
  }

  // Get CSRF token for Laravel Sanctum
  async getCsrfToken(): Promise<void> {
    await fetch(`${this.sanctumURL}/sanctum/csrf-cookie`, {
      method: 'GET',
      credentials: 'include',
    });
  }

  async getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
  }


  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        'X-XSRF-TOKEN': await this.getCookie('XSRF-TOKEN'), // send the token here

        ...options.headers,
      },
      credentials: 'include', // Always include cookies
      ...options,
    };

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);

      if (!response.ok) {
        if (response.status === 401) {
          // Clear any stored user data and redirect to login
          sessionStorage.removeItem('user');
          window.location.href = '/login';
          throw new Error('Unauthorized');
        }

        const errorData = await response.json();
        throw new Error(errorData.message || 'An error occurred');
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    // Get CSRF token before POST requests
    await this.getCsrfToken();

    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    await this.getCsrfToken();

    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    await this.getCsrfToken();

    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export const apiService = new ApiService(API_BASE_URL, SANCTUM_URL);
export default apiService;
