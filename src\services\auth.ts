import apiService from './api';

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  phone: string;
  roles: string[];
  permissions: string[];
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  password: string;
  password_confirmation: string;
}

export interface AuthResponse {
  message: string;
  user: User;
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<User> {
    const response = await apiService.post<AuthResponse>('/auth/login', credentials);

    if (response.data?.user) {
      // Store user data in sessionStorage (no token needed with Sanctum sessions)
      sessionStorage.setItem('user', JSON.stringify(response.data.user));
      return response.data.user;
    }

    throw new Error('Login failed');
  }

  async register(data: RegisterData): Promise<User> {
    const response = await apiService.post<AuthResponse>('/auth/register', data);

    if (response.data?.user) {
      // Store user data in sessionStorage
      sessionStorage.setItem('user', JSON.stringify(response.data.user));
      return response.data.user;
    }

    throw new Error('Registration failed');
  }

  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // Continue with local logout even if API call fails
      console.error('Logout API call failed:', error);
    } finally {
      // Clear session storage
      sessionStorage.removeItem('user');
    }
  }

  async getCurrentUser(): Promise<User> {
      const response = await apiService.get<{ user: User }>('/auth/user');

    if (response.data?.user) {
      // Update stored user data
      sessionStorage.setItem('user', JSON.stringify(response.data.user));
      return response.data.user;
    }

    throw new Error('Failed to get current user');
  }

  getStoredUser(): User | null {
    const userStr = sessionStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    // Check if user data exists in session storage
    return !!this.getStoredUser();
  }

  hasRole(role: string): boolean {
    const user = this.getStoredUser();
    return user?.roles.includes(role) || false;
  }

  hasPermission(permission: string): boolean {
    const user = this.getStoredUser();
    return user?.permissions.includes(permission) || false;
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.getStoredUser();
    return roles.some(role => user?.roles.includes(role)) || false;
  }

  hasAnyPermission(permissions: string[]): boolean {
    const user = this.getStoredUser();
    return permissions.some(permission => user?.permissions.includes(permission)) || false;
  }

  // Clear all authentication data
  clearAuth(): void {
    sessionStorage.removeItem('user');
  }
}

export const authService = new AuthService();
export default authService;
